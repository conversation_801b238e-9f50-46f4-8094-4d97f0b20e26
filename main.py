# from sqlalchemy import create_engine
# from sqlalchemy.orm import sessionmaker, declarative_base
#
#
# engine = create_engine("postgresql://user:password@localhost:5432/finance-erp")  # update with actual details
# SessionLocal = sessionmaker(bind=engine)
#
# Base = declarative_base()
#
# from object_registry import locate_instance
# from finance_erp.domain.payment.repository.payment_repository import PaymentRepository
# from finance_erp.domain.payment.entity.payment import PGPaymentEntity
# from datetime import datetime, date
# import time
# from finance_erp.application.payments.commands.bulk_ingest_payment_report import BulkIngestPaymentReportCommandHandler
#
#
# def test_job_queue_duplicate():
#     """
#     Simulate the same job being processed multiple times
#     """
#
#     def process_payment_job(job_data):
#         handler: BulkIngestPaymentReportCommandHandler = locate_instance(BulkIngestPaymentReportCommandHandler)
#
#         # Simulate processing the same job data multiple times
#         payment = PGPaymentEntity(**job_data)
#
#         try:
#             handler._run_ingestion(
#                 report_name="payment",
#                 ingestion_data_list=[job_data],
#                 event_id="test_event_id"
#             )
#             print("Job processed successfully")
#         except Exception as e:
#             raise e
#
#     # Same job data processed multiple times
#     job_data = {
#     "pg_charges": None,
#     "pg_tax": None,
#     "platform_fees": None,
#     "pg_transaction_id": "R2GEKC2I2Y",
#     "reference_number": "B2B-88250003271657",
#     "hotel_code": "1684992",
#     "paid_by": "treebo",
#     "paid_to": "corporate",
#     "payment_type": "PTT",
#     "payment_amount": -999,
#     "paymode": "transferred_credit",
#     "paymode_type": None,
#     "payor_entity": "tzzsc093",
#     "athena_code": "tzzsc093",
#     "payor_name": "UTAZZO SERVICES PRIVATE LIMITED ",
#     "hotel_name": "Umaiyyal Home Stay Churned",
#     "invoice_id": None,
#     "channel": "b2b",
#     "sub_channel": "large-corporate",
#     "original_booking_amount": 999,
#     "is_advance": False,
#     "refund_reason": "Excess Payment",
#     "posting_date": "2025-06-28",
#     "payment_date": "2025-06-28",
#     "booker_entity": "tzzsc093",
#     "booking_owner": {
#       "name": "UTAZZO SERVICES PRIVATE LIMITED",
#       "phone": "9322800100",
#       "email": None
#     },
#     "check_in": "2025-03-20",
#     "check_out": "2025-03-22",
#     "seller_model": "RESELLER",
#     "uu_id": "BIL-160325-1605-6799-3443-2-1/B2B-88250003271657"
#   }
#
#     # Process same job multiple times
#     for i in range(3):
#         print(f"Processing job attempt {i + 1}")
#         process_payment_job(job_data)
#         time.sleep(0.1)
#
# test_job_queue_duplicate()
from finance_erp.async_job.job.repositories.job_repository import JobRepository
from finance_erp.async_job.job_consumer import JobConsumer
from object_registry import locate_instance

job_repository: JobRepository = locate_instance(JobRepository)

job_aggregate = job_repository.get_job(pk="JOB-310725-0932-4635-2853")
print("Job aggregate: ", job_aggregate)

job_consumer: JobConsumer = locate_instance(JobConsumer)
job_consumer.process_job(
    job_aggregate=job_aggregate
)